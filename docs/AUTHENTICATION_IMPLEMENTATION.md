# Implementación de Autenticación con Fluent UI

Esta implementación proporciona una interfaz completa de autenticación para consumir la API de ecommerce usando componentes de Fluent UI.

## 🚀 Características Implementadas

### ✅ Funcionalidades de Autenticación
- **Login de usuarios** con validación de formularios
- **Registro de nuevos usuarios** con roles personalizables
- **Perfil de usuario** con información detallada
- **Logout seguro** con limpieza de tokens
- **Manejo de sesiones** con JWT tokens
- **Gestión de errores** con mensajes informativos

### 🎨 Componentes de UI
- **LoginForm**: Formulario de inicio de sesión con iconos y validación
- **SignupForm**: Formulario de registro con campos opcionales
- **UserProfile**: Panel de información del usuario con roles visuales
- **AuthContainer**: Contenedor principal que maneja el estado de autenticación

### 🔧 Arquitectura Técnica
- **Context API** para manejo de estado global de autenticación
- **TypeScript** con tipos estrictos para la API
- **Servicios modulares** para llamadas a la API
- **Configuración centralizada** para endpoints
- **LocalStorage** para persistencia de sesión

## 📁 Estructura de Archivos

```
src/
├── components/
│   ├── AuthContainer.tsx      # Contenedor principal
│   ├── LoginForm.tsx          # Formulario de login
│   ├── SignupForm.tsx         # Formulario de registro
│   └── UserProfile.tsx        # Perfil de usuario
├── contexts/
│   └── AuthContext.tsx        # Context de autenticación
├── services/
│   └── authService.ts         # Servicio de API
├── types/
│   └── auth.ts               # Tipos TypeScript
├── config/
│   └── api.ts                # Configuración de API
└── App.tsx                   # Aplicación principal
```

## 🔌 Configuración de la API

### Variables de Entorno
Crea un archivo `.env` en la raíz del proyecto:

```env
REACT_APP_API_URL=http://localhost:8080
```

### Endpoints Utilizados
- `POST /api/auth/login` - Iniciar sesión
- `POST /api/auth/signup` - Registrar usuario
- `GET /api/auth/session-info` - Información de sesión
- `POST /api/auth/logout` - Cerrar sesión

## 🎯 Roles de Usuario

La aplicación maneja tres tipos de roles:

| Rol | Descripción | Icono | Color |
|-----|-------------|-------|-------|
| **ROLE_ADMIN** | Administrador del sistema | AdminALogoInverse32 | Rojo |
| **ROLE_VENDEDOR** | Vendedor de productos | Shop | Verde |
| **ROLE_USUARIO** | Usuario regular | Contact | Azul |

## 🔒 Seguridad

### JWT Token Management
- Los tokens se almacenan en `localStorage`
- Se incluyen automáticamente en las cabeceras de autorización
- Se limpian al cerrar sesión o en caso de error

### Validación de Formularios
- **Username**: 3-20 caracteres, requerido
- **Email**: Formato válido, máximo 50 caracteres
- **Password**: 6-40 caracteres, requerido
- **Nombres**: Opcionales, sin restricciones específicas

## 🎨 Componentes de Fluent UI Utilizados

### Formularios
- `TextField` - Campos de entrada con iconos
- `Dropdown` - Selector de roles
- `PrimaryButton` - Botones principales
- `DefaultButton` - Botones secundarios

### Layout y Navegación
- `Stack` - Contenedores flexibles
- `Text` - Tipografía con variantes
- `Icon` - Iconos de Microsoft Design Language

### Feedback y Estados
- `MessageBar` - Mensajes de error y éxito
- `Persona` - Avatar de usuario
- Estados de carga en botones

## 🚀 Uso

### Iniciar la Aplicación
```bash
npm run dev
# o
pnpm dev
```

### Credenciales de Prueba
Según la documentación de la API, existe un usuario administrador por defecto:
- **Username**: `admin`
- **Password**: `admin123`

### Flujo de Usuario
1. **Primera visita**: Se muestra el formulario de login
2. **Registro**: Cambiar a formulario de registro si es necesario
3. **Login exitoso**: Se muestra el perfil del usuario
4. **Navegación**: Botones para editar perfil o cerrar sesión

## 🔧 Personalización

### Cambiar URL de la API
Modifica el archivo `src/config/api.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: 'https://tu-api.com',
  // ...
};
```

### Agregar Nuevos Roles
Actualiza el enum en `src/types/auth.ts`:

```typescript
export enum UserRole {
  ADMIN = 'ROLE_ADMIN',
  VENDEDOR = 'ROLE_VENDEDOR',
  USUARIO = 'ROLE_USUARIO',
  NUEVO_ROL = 'ROLE_NUEVO_ROL'
}
```

### Personalizar Estilos
Los componentes usan el sistema de estilos de Fluent UI. Puedes personalizar:
- Colores del tema
- Espaciado y tokens
- Estilos específicos de componentes

## 🐛 Manejo de Errores

La aplicación maneja varios tipos de errores:

- **401 Unauthorized**: Credenciales incorrectas
- **400 Bad Request**: Datos de formulario inválidos
- **500 Server Error**: Errores del servidor
- **Network Error**: Problemas de conectividad

Los errores se muestran usando `MessageBar` con iconos y colores apropiados.

## 📱 Responsive Design

Los componentes están diseñados para ser responsivos:
- Anchos fijos para formularios (400-500px)
- Centrado automático
- Espaciado consistente con tokens de Fluent UI
- Adaptación a diferentes tamaños de pantalla

## 🔄 Estado de la Aplicación

El estado se maneja a través del `AuthContext`:

```typescript
interface AuthContextType {
  user: User | null;           // Usuario actual
  token: string | null;        // JWT token
  login: (credentials) => Promise<void>;
  signup: (userData) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;          // Estado de carga
  error: string | null;        // Errores actuales
}
```

## 🎯 Próximas Funcionalidades

- [ ] Edición de perfil de usuario
- [ ] Recuperación de contraseña
- [ ] Autenticación con redes sociales
- [ ] Verificación de email
- [ ] Configuración de tema personalizado
- [ ] Notificaciones push
